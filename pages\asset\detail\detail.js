// pages/asset/detail/detail.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'
import AssetAPI from '../../../utils/asset-api.js'

Page({
  data: {
    // 资产ID
    assetId: '',
    
    // 资产详情
    assetDetail: null,
    
    // 标签页
    activeTab: 'basic',
    
    // 维护相关
    maintenancePlans: [],
    maintenanceTasks: [],
    
    // 关联备件
    assetParts: [],
    
    // 操作记录
    operationRecords: [],
    
    // 加载状态
    loading: true,
    hasError: false,
    
    // 图片预览
    showImagePreview: false,
    previewImageUrl: ''
  },

  onLoad(options) {
    console.log('📋 资产详情页面加载，参数:', options)

    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 获取资产ID
    const { assetId } = options
    if (!assetId) {
      console.error('❌ 资产ID为空')
      Toast.fail('资产ID不能为空')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    console.log('✅ 资产ID:', assetId)
    this.setData({ assetId })
    
    // 加载资产详情
    this.loadAssetDetail()
  },

  onShow() {
    // 页面显示时可以刷新数据（如果需要）
  },

  /**
   * 加载资产详情
   */
  async loadAssetDetail() {
    try {
      this.setData({ loading: true, hasError: false })
      
      console.log('📤 请求资产详情，ID:', this.data.assetId)
      
      // 调用API获取资产详情（含维护信息）
      const response = await AssetAPI.getAssetDetailWithMaintenance(this.data.assetId)
      
      console.log('📥 资产详情响应:', response)

      // 使用新的API响应处理方法
      const assetDetail = AssetAPI.handleApiResponse(response)

      // 格式化资产数据
      const formattedAsset = this.formatAssetDetail(assetDetail)

      // 更新页面标题
      wx.setNavigationBarTitle({
        title: assetDetail.assetName || '资产详情'
      })

      this.setData({
        assetDetail: formattedAsset,
        maintenancePlans: assetDetail.maintenancePlans || [],
        maintenanceTasks: AssetAPI.formatMaintenanceTasks(assetDetail.maintenanceTasks || [])
      })

      // 加载关联备件
      this.loadAssetParts()

      console.log('✅ 资产详情加载成功:', formattedAsset)
    } catch (error) {
      console.error('❌ 加载资产详情失败:', error)
      this.setData({ hasError: true })
      Toast.fail('获取资产详情失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 加载关联备件
   */
  async loadAssetParts() {
    try {
      console.log('📤 请求关联备件，资产ID:', this.data.assetId)
      
      const response = await AssetAPI.getAssetParts(this.data.assetId)
      
      console.log('📥 关联备件响应:', response)

      if (response && response.code === 200) {
        const assetParts = response.data || []
        this.setData({ assetParts })
        
        console.log('✅ 关联备件加载成功，共', assetParts.length, '个')
      } else {
        console.warn('⚠️ 获取关联备件失败:', response?.msg)
      }
    } catch (error) {
      console.error('❌ 加载关联备件失败:', error)
      // 关联备件加载失败不影响主要功能，只记录错误
    }
  },

  /**
   * 格式化资产详情数据
   */
  formatAssetDetail(assetDetail) {
    return {
      ...assetDetail,
      statusName: AssetAPI.getAssetStatusName(assetDetail.status),
      statusColor: AssetAPI.getAssetStatusColor(assetDetail.status),
      statusType: AssetAPI.getAssetStatusType(assetDetail.status),
      formattedPrice: assetDetail.purchasePrice ? `¥${Number(assetDetail.purchasePrice).toFixed(2)}` : '未设置',
      formattedPurchaseDate: assetDetail.purchaseDate ? AssetAPI.formatDate(assetDetail.purchaseDate) : '未设置',
      images: assetDetail.images || [],
      // 处理规格信息
      specificationsText: AssetAPI.formatSpecifications(assetDetail.specifications),
      // 处理保修信息
      warrantyText: AssetAPI.formatWarrantyInfo(assetDetail.warrantyInfo),
      // 格式化保修日期
      formattedWarrantyStart: assetDetail.warrantyInfo?.warrantyStartDate ?
        AssetAPI.formatDate(assetDetail.warrantyInfo.warrantyStartDate) : '未设置',
      formattedWarrantyEnd: assetDetail.warrantyInfo?.warrantyEndDate ?
        AssetAPI.formatDate(assetDetail.warrantyInfo.warrantyEndDate) : '未设置',
      // 格式化创建和更新时间
      formattedCreateTime: assetDetail.createTime ?
        this.formatDateTime(assetDetail.createTime) : '未设置',
      formattedUpdateTime: assetDetail.updateTime ?
        this.formatDateTime(assetDetail.updateTime) : '未设置'
    }
  },

  /**
   * 格式化维护任务数据
   */
  formatMaintenanceTasks(tasks) {
    return tasks.map(task => ({
      ...task,
      statusName: this.getTaskStatusName(task.status),
      formattedDate: task.scheduledDate ? AssetAPI.formatDate(task.scheduledDate) : '未设置'
    }))
  },

  /**
   * 获取任务状态名称
   */
  getTaskStatusName(status) {
    const statusMap = {
      'pending': '待执行',
      'in_progress': '执行中',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return statusMap[status] || '未知'
  },

  /**
   * 格式化日期时间
   * @param {string} dateTimeString 日期时间字符串
   * @returns {string} 格式化后的日期时间
   */
  formatDateTime(dateTimeString) {
    if (!dateTimeString) return ''

    try {
      const date = new Date(dateTimeString)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}`
    } catch (error) {
      console.error('日期时间格式化失败:', error)
      return dateTimeString
    }
  },

  /**
   * 标签页切换
   */
  onTabChange(event) {
    const activeTab = event.detail.name
    console.log('切换标签页:', activeTab)
    
    this.setData({ activeTab })
    
    // 根据标签页加载对应数据
    switch (activeTab) {
      case 'operations':
        this.loadOperationRecords()
        break
    }
  },

  /**
   * 加载操作记录
   */
  async loadOperationRecords() {
    // 这里可以调用操作记录API
    // 暂时使用模拟数据
    const mockRecords = [
      {
        recordId: '1',
        operationType: '创建资产',
        description: '资产首次录入系统',
        formattedDate: '2024-01-15'
      },
      {
        recordId: '2',
        operationType: '状态变更',
        description: '资产状态从正常变更为维修中',
        formattedDate: '2024-01-20'
      }
    ]
    
    this.setData({ operationRecords: mockRecords })
  },

  /**
   * 重试加载
   */
  onRetryLoad() {
    this.loadAssetDetail()
  },

  /**
   * 预览图片
   */
  previewImage(event) {
    const { index } = event.currentTarget.dataset
    const images = this.data.assetDetail.images
    
    if (images && images[index]) {
      // 使用微信原生图片预览
      wx.previewImage({
        current: images[index],
        urls: images
      })
    }
  },

  /**
   * 查看维护计划
   */
  viewMaintenancePlan(event) {
    const plan = event.currentTarget.dataset.plan
    console.log('查看维护计划:', plan)
    Toast.fail('维护计划详情功能开发中')
  },

  /**
   * 查看维护任务
   */
  viewMaintenanceTask(event) {
    const task = event.currentTarget.dataset.task
    console.log('查看维护任务:', task)
    Toast.fail('维护任务详情功能开发中')
  },

  /**
   * 查看备件详情
   */
  viewPartDetail(event) {
    const part = event.currentTarget.dataset.part
    console.log('查看备件详情:', part)
    Toast.fail('备件详情功能开发中')
  },

  /**
   * 关闭图片预览
   */
  closeImagePreview() {
    this.setData({ 
      showImagePreview: false,
      previewImageUrl: ''
    })
  }
})
