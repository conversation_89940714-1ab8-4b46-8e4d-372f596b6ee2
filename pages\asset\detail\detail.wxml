<!--pages/asset/detail/detail.wxml-->
<view class="page-container">

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{ loading }}">
    <van-loading type="spinner" size="32px">加载中...</van-loading>
  </view>

  <!-- 资产详情内容 -->
  <view class="detail-content" wx:elif="{{ assetDetail }}">

    <!-- 资产基本信息 -->
    <view class="basic-info-section">
      <!-- 资产图片 -->
      <view class="asset-images" wx:if="{{ assetDetail.images && assetDetail.images.length > 0 }}">
        <scroll-view class="image-scroll" scroll-x>
          <view class="image-list">
            <view 
              class="image-item"
              wx:for="{{ assetDetail.images }}"
              wx:key="index"
              bind:tap="previewImage"
              data-index="{{ index }}"
            >
              <van-image
                src="{{ item }}"
                width="200rpx"
                height="200rpx"
                radius="12rpx"
                fit="cover"
                error-icon="photo-fail"
              />
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 资产标题信息 -->
      <view class="asset-header">
        <view class="asset-title">
          <text class="asset-name">{{ assetDetail.assetName }}</text>
          <van-tag
            type="{{ assetDetail.statusType }}"
            size="medium"
          >
            {{ assetDetail.statusName }}
          </van-tag>
        </view>
        <text class="asset-code">编码: {{ assetDetail.assetCode }}</text>
      </view>
    </view>

    <!-- 标签页 -->
    <van-tabs active="{{ activeTab }}" bind:change="onTabChange" sticky>
      <!-- 基本信息 -->
      <van-tab title="基本信息" name="basic">
        <view class="tab-content">
          <van-cell-group>
            <van-cell title="资产名称" value="{{ assetDetail.assetName }}" />
            <van-cell title="资产编码" value="{{ assetDetail.assetCode }}" />
            <van-cell title="资产类型" value="{{ assetDetail.assetType || '未设置' }}" />
            <van-cell title="资产状态" use-slot>
              <van-tag type="{{ assetDetail.statusType }}" size="small">
                {{ assetDetail.statusName }}
              </van-tag>
            </van-cell>
            <van-cell title="存放位置" value="{{ assetDetail.location || '未设置' }}" />
            <van-cell title="所属部门" value="{{ assetDetail.deptName || '未设置' }}" />
            <van-cell title="型号规格" value="{{ assetDetail.model || '未设置' }}" />
            <van-cell title="供应商" value="{{ assetDetail.supplier || '未设置' }}" />
            <van-cell title="采购价格" value="{{ assetDetail.formattedPrice }}" />
            <van-cell title="采购日期" value="{{ assetDetail.formattedPurchaseDate }}" />
            <van-cell
              wx:if="{{ assetDetail.description }}"
              title="资产描述"
              value="{{ assetDetail.description }}"
              label="详细描述信息"
            />
          </van-cell-group>

          <!-- 规格参数 -->
          <van-cell-group wx:if="{{ assetDetail.specifications }}" title="规格参数">
            <van-cell
              wx:if="{{ assetDetail.specifications.cpu }}"
              title="处理器"
              value="{{ assetDetail.specifications.cpu }}"
            />
            <van-cell
              wx:if="{{ assetDetail.specifications.memory }}"
              title="内存"
              value="{{ assetDetail.specifications.memory }}"
            />
            <van-cell
              wx:if="{{ assetDetail.specifications.storage }}"
              title="存储"
              value="{{ assetDetail.specifications.storage }}"
            />
            <van-cell
              wx:if="{{ assetDetail.specifications.network }}"
              title="网络"
              value="{{ assetDetail.specifications.network }}"
            />
          </van-cell-group>

          <!-- 保修信息 -->
          <van-cell-group wx:if="{{ assetDetail.warrantyInfo }}" title="保修信息">
            <van-cell
              wx:if="{{ assetDetail.warrantyInfo.warrantyPeriod }}"
              title="保修期限"
              value="{{ assetDetail.warrantyInfo.warrantyPeriod }}"
            />
            <van-cell
              title="保修开始"
              value="{{ assetDetail.formattedWarrantyStart }}"
            />
            <van-cell
              title="保修结束"
              value="{{ assetDetail.formattedWarrantyEnd }}"
            />
          </van-cell-group>
        </view>
      </van-tab>

      <!-- 维护记录 -->
      <van-tab title="维护记录" name="maintenance">
        <view class="tab-content">
          <!-- 维护计划 -->
          <view class="maintenance-section" wx:if="{{ maintenancePlans.length > 0 }}">
            <van-divider content-position="left">维护计划</van-divider>
            <van-cell-group>
              <van-cell
                wx:for="{{ maintenancePlans }}"
                wx:key="planId"
                title="{{ item.planName }}"
                label="{{ item.description }}"
                value="{{ item.nextMaintenanceDate }}"
                is-link
                bind:click="viewMaintenancePlan"
                data-plan="{{ item }}"
              />
            </van-cell-group>
          </view>

          <!-- 维护任务 -->
          <view class="maintenance-section" wx:if="{{ maintenanceTasks.length > 0 }}">
            <van-divider content-position="left">维护任务</van-divider>
            <van-cell-group>
              <van-cell
                wx:for="{{ maintenanceTasks }}"
                wx:key="taskId"
                title="{{ item.taskName }}"
                label="{{ item.description }}"
                use-slot
                is-link
                bind:click="viewMaintenanceTask"
                data-task="{{ item }}"
              >
                <view class="task-info">
                  <van-tag
                    type="{{ item.statusType }}"
                    size="small"
                  >
                    {{ item.statusName }}
                  </van-tag>
                  <text class="task-date">{{ item.formattedScheduledDate }}</text>
                  <text wx:if="{{ item.isCompleted && item.formattedCompletedDate }}" class="completed-date">
                    (完成: {{ item.formattedCompletedDate }})
                  </text>
                </view>
              </van-cell>
            </van-cell-group>
          </view>

          <!-- 无维护记录 -->
          <van-empty
            wx:if="{{ maintenancePlans.length === 0 && maintenanceTasks.length === 0 }}"
            image="search"
            description="暂无维护记录"
          />
        </view>
      </van-tab>

      <!-- 关联备件 -->
      <van-tab title="关联备件" name="parts">
        <view class="tab-content">
          <van-cell-group wx:if="{{ assetParts.length > 0 }}">
            <van-cell
              wx:for="{{ assetParts }}"
              wx:key="relationId"
              title="{{ item.partName }}"
              label="编码: {{ item.partCode }}"
              value="数量: {{ item.quantity }}"
              is-link
              bind:click="viewPartDetail"
              data-part="{{ item }}"
            />
          </van-cell-group>

          <!-- 无关联备件 -->
          <van-empty
            wx:if="{{ assetParts.length === 0 }}"
            image="search"
            description="暂无关联备件"
          />
        </view>
      </van-tab>

      <!-- 操作记录 -->
      <van-tab title="操作记录" name="operations">
        <view class="tab-content">
          <van-cell-group wx:if="{{ operationRecords.length > 0 }}">
            <van-cell
              wx:for="{{ operationRecords }}"
              wx:key="recordId"
              title="{{ item.operationType }}"
              label="{{ item.description }}"
              value="{{ item.formattedDate }}"
              border="{{ false }}"
            />
          </van-cell-group>

          <!-- 无操作记录 -->
          <van-empty
            wx:if="{{ operationRecords.length === 0 }}"
            image="search"
            description="暂无操作记录"
          />
        </view>
      </van-tab>
    </van-tabs>

  </view>

  <!-- 错误状态 -->
  <van-empty
    wx:elif="{{ hasError }}"
    image="error"
    description="加载失败，请重试"
  >
    <van-button
      round
      type="primary"
      size="small"
      bind:tap="onRetryLoad"
    >
      重新加载
    </van-button>
  </van-empty>

  <!-- 图片预览弹窗 -->
  <van-popup
    show="{{ showImagePreview }}"
    position="center"
    round
    bind:close="closeImagePreview"
    custom-class="image-preview-popup"
  >
    <view class="image-preview">
      <van-image
        src="{{ previewImageUrl }}"
        width="600rpx"
        height="600rpx"
        fit="contain"
        error-icon="photo-fail"
      />
      <view class="preview-actions">
        <van-button
          type="default"
          size="small"
          bind:tap="closeImagePreview"
        >
          关闭
        </van-button>
      </view>
    </view>
  </van-popup>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
