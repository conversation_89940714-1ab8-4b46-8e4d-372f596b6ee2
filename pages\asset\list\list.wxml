<!--pages/asset/list/list.wxml-->
<view class="page-container">

  <!-- 搜索区域 -->
  <view class="search-section">
    <van-search
      value="{{ searchKeyword }}"
      placeholder="搜索资产名称、编码或位置"
      bind:search="onSearch"
      bind:input="onSearchInput"
      bind:clear="onSearchClear"
      use-action-slot
    >
      <view slot="action" class="search-actions">
        <van-icon name="filter-o" size="20" bind:tap="showFilterPopup" />
        <van-icon name="scan" size="20" bind:tap="onScanCode" />
      </view>
    </van-search>
  </view>

  <!-- 筛选条件显示 -->
  <view class="filter-tags" wx:if="{{ hasActiveFilters }}">
    <van-tag
      wx:if="{{ filterParams.status }}"
      type="primary"
      size="small"
      closeable
      bind:close="clearStatusFilter"
    >
      状态: {{ getStatusName(filterParams.status) }}
    </van-tag>
    <van-tag
      wx:if="{{ filterParams.assetType }}"
      type="primary"
      size="small"
      closeable
      bind:close="clearTypeFilter"
    >
      类型: {{ filterParams.assetType }}
    </van-tag>
    <van-tag
      wx:if="{{ filterParams.location }}"
      type="primary"
      size="small"
      closeable
      bind:close="clearLocationFilter"
    >
      位置: {{ filterParams.location }}
    </van-tag>
    <van-button
      type="default"
      size="mini"
      bind:tap="clearAllFilters"
      custom-class="clear-all-btn"
    >
      清除全部
    </van-button>
  </view>

  <!-- 资产列表 -->
  <view class="list-section">
    <scroll-view 
      class="scroll-container"
      scroll-y
      refresher-enabled
      refresher-triggered="{{ refreshing }}"
      bind:refresherrefresh="onRefresh"
      bind:scrolltolower="onLoadMore"
    >
      <!-- 加载状态 -->
      <view class="loading-container" wx:if="{{ loading && assetList.length === 0 }}">
        <van-loading type="spinner" size="24px">加载中...</van-loading>
      </view>

      <!-- 资产列表 -->
      <view class="asset-list" wx:elif="{{ assetList.length > 0 }}">
        <van-cell
          wx:for="{{ assetList }}"
          wx:key="assetId"
          bind:click="onAssetItemClick"
          data-asset="{{ item }}"
          custom-class="asset-item"
          use-slot
        >
          <view class="asset-content">
            <!-- 资产图片 -->
            <view class="asset-image">
              <van-image
                src="{{ item.imageUrl || '/images/default-asset.png' }}"
                width="80rpx"
                height="80rpx"
                radius="8rpx"
                error-icon="photo-fail"
              />
            </view>

            <!-- 资产信息 -->
            <view class="asset-info">
              <view class="asset-header">
                <text class="asset-name">{{ item.assetName }}</text>
                <van-tag
                  type="{{ item.statusType }}"
                  size="small"
                >
                  {{ item.statusName }}
                </van-tag>
              </view>

              <view class="asset-details">
                <view class="detail-row">
                  <text class="label">编码:</text>
                  <text class="value">{{ item.assetCode }}</text>
                </view>
                <view class="detail-row" wx:if="{{ item.assetType }}">
                  <text class="label">类型:</text>
                  <text class="value">{{ item.assetType }}</text>
                </view>
                <view class="detail-row" wx:if="{{ item.location }}">
                  <text class="label">位置:</text>
                  <text class="value">{{ item.location }}</text>
                </view>
                <view class="detail-row" wx:if="{{ item.deptName }}">
                  <text class="label">部门:</text>
                  <text class="value">{{ item.deptName }}</text>
                </view>
                <view class="detail-row" wx:if="{{ item.formattedPrice }}">
                  <text class="label">价格:</text>
                  <text class="value price">{{ item.formattedPrice }}</text>
                </view>
              </view>
            </view>

            <!-- 箭头图标 -->
            <view class="asset-arrow">
              <van-icon name="arrow" size="16" color="#c8c9cc" />
            </view>
          </view>
        </van-cell>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{ !finished }}">
          <van-loading wx:if="{{ loading }}" type="spinner" size="20px">加载中...</van-loading>
          <text wx:else class="load-more-text">上拉加载更多</text>
        </view>

        <!-- 没有更多数据 -->
        <view class="no-more" wx:if="{{ finished && assetList.length > 0 }}">
          <text class="no-more-text">没有更多数据了</text>
        </view>
      </view>

      <!-- 空状态 -->
      <van-empty
        wx:else
        image="search"
        description="{{ searchKeyword ? '未找到相关资产' : '暂无资产数据' }}"
      >
        <van-button
          wx:if="{{ !searchKeyword }}"
          round
          type="primary"
          size="small"
          bind:tap="onRefresh"
        >
          刷新数据
        </van-button>
      </van-empty>
    </scroll-view>
  </view>

  <!-- 筛选弹窗 -->
  <van-popup
    show="{{ showFilter }}"
    position="bottom"
    round
    bind:close="hideFilterPopup"
  >
    <view class="filter-popup">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <van-button
          type="primary"
          size="small"
          bind:tap="applyFilter"
        >
          确定
        </van-button>
      </view>

      <view class="filter-content">
        <!-- 资产状态筛选 -->
        <view class="filter-section">
          <text class="filter-label">资产状态</text>
          <van-picker
            columns="{{ statusOptions }}"
            value-key="label"
            bind:change="onStatusChange"
            default-index="{{ selectedStatusIndex }}"
          />
        </view>

        <!-- 资产类型筛选 -->
        <view class="filter-section">
          <text class="filter-label">资产类型</text>
          <van-field
            value="{{ tempFilterParams.assetType }}"
            placeholder="请输入资产类型"
            bind:change="onTypeChange"
          />
        </view>

        <!-- 存放位置筛选 -->
        <view class="filter-section">
          <text class="filter-label">存放位置</text>
          <van-field
            value="{{ tempFilterParams.location }}"
            placeholder="请输入存放位置"
            bind:change="onLocationChange"
          />
        </view>
      </view>

      <view class="filter-footer">
        <van-button
          type="default"
          size="large"
          bind:tap="resetFilter"
          custom-class="reset-btn"
        >
          重置
        </van-button>
        <van-button
          type="primary"
          size="large"
          bind:tap="applyFilter"
          custom-class="apply-btn"
        >
          应用筛选
        </van-button>
      </view>
    </view>
  </van-popup>

  <!-- Toast 组件 -->
  <van-toast id="van-toast" />
</view>
