// pages/asset/index.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../utils/permission.js'

Page({
  data: {
    // 子菜单选项
    menuItems: [
      {
        id: 'asset-ledger',
        icon: 'records',
        title: '资产台账',
        subtitle: '查看和管理资产信息',
        color: '#1989fa',
        page: '/pages/asset/list/list'
      },
      {
        id: 'asset-search',
        icon: 'search',
        title: '资产搜索',
        subtitle: '快速搜索和查找资产',
        color: '#ff976a',
        page: '/pages/asset/search/search'
      },
      {
        id: 'asset-statistics',
        icon: 'bar-chart-o',
        title: '资产统计',
        subtitle: '资产数据统计分析',
        color: '#07c160',
        page: ''
      },
      {
        id: 'asset-maintenance',
        icon: 'setting-o',
        title: '维护管理',
        subtitle: '资产维护计划和记录',
        color: '#ee0a24',
        page: ''
      },
      {
        id: 'asset-reports',
        icon: 'description',
        title: '资产报表',
        subtitle: '生成各类资产报表',
        color: '#7232dd',
        page: ''
      },
      {
        id: 'asset-settings',
        icon: 'manager-o',
        title: '资产设置',
        subtitle: '资产分类和参数配置',
        color: '#9c26b0',
        page: ''
      }
    ]
  },

  onLoad() {
    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.refreshData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    // 页面初始化完成
    console.log('资产管理子菜单初始化完成')
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      // 这里可以加载一些统计数据
      console.log('资产管理数据刷新完成')
    } catch (error) {
      console.error('刷新数据失败:', error)
    }
  },

  /**
   * 菜单项点击
   */
  onMenuItemClick(event) {
    const item = event.currentTarget.dataset.item
    console.log('点击菜单项:', item)

    if (item.page) {
      // 跳转到对应页面
      wx.navigateTo({
        url: item.page,
        fail: () => {
          Toast.fail(`${item.title}功能开发中`)
        }
      })
    } else {
      Toast.fail(`${item.title}功能开发中`)
    }
  }
})
