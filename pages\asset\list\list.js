// pages/asset/list/list.js
import Toast from '@vant/weapp/toast/toast';
import { checkPagePermission } from '../../../utils/permission.js'
import AssetAPI from '../../../utils/asset-api.js'

Page({
  data: {
    // 搜索相关
    searchKeyword: '',
    
    // 资产列表
    assetList: [],
    
    // 分页信息
    pageInfo: {
      current: 1,
      size: 20,
      total: 0
    },
    
    // 加载状态
    loading: false,
    refreshing: false,
    finished: false,
    
    // 筛选相关
    showFilter: false,
    filterParams: {},
    tempFilterParams: {},
    hasActiveFilters: false,
    
    // 状态选项
    statusOptions: [],
    selectedStatusIndex: 0,
    
    // 搜索防抖定时器
    searchTimer: null
  },

  onLoad(options) {
    console.log('📋 资产列表页面加载，参数:', options)

    // 检查页面权限
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentUrl = '/' + currentPage.route

    if (!checkPagePermission(currentUrl)) {
      return
    }

    // 初始化页面数据
    this.initPageData()
    
    // 处理来自搜索页面的参数
    if (options.keyword) {
      this.setData({
        searchKeyword: decodeURIComponent(options.keyword)
      })
    }
    
    // 加载资产列表
    this.loadAssetList(true)
  },

  onShow() {
    // 页面显示时刷新数据（如果需要）
    if (this.data.assetList.length === 0) {
      this.loadAssetList(true)
    }
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.onRefresh()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    // 初始化状态选项
    const statusOptions = [
      { value: '', label: '全部状态' },
      ...AssetAPI.getAssetStatusOptions()
    ]
    
    this.setData({
      statusOptions: statusOptions
    })
    
    console.log('✅ 资产列表页面初始化完成')
  },

  /**
   * 加载资产列表
   */
  async loadAssetList(isRefresh = false) {
    if (this.data.loading) return
    
    // 如果是刷新，重置分页
    if (isRefresh) {
      this.setData({
        'pageInfo.current': 1,
        finished: false
      })
    }
    
    this.setData({ loading: true })

    try {
      // 构建查询参数
      const params = {
        pageNum: this.data.pageInfo.current,
        pageSize: this.data.pageInfo.size,
        ...this.data.filterParams
      }

      // 添加搜索关键词
      if (this.data.searchKeyword && this.data.searchKeyword.trim()) {
        params.keyword = this.data.searchKeyword.trim()
      }

      console.log('📤 请求资产列表，参数:', params)

      // 调用API
      const response = await AssetAPI.getAssetList(params)
      
      console.log('📥 资产列表响应:', response)

      if (response && response.code === 200) {
        // 处理响应数据
        const data = response.data || {}
        const records = data.records || []
        const total = data.total || 0
        
        // 格式化资产数据
        const formattedAssets = AssetAPI.formatAssetList(records)
        
        // 更新列表数据
        const newAssetList = isRefresh ? formattedAssets : [...this.data.assetList, ...formattedAssets]
        
        // 判断是否还有更多数据
        const finished = newAssetList.length >= total || records.length < this.data.pageInfo.size
        
        this.setData({
          assetList: newAssetList,
          'pageInfo.total': total,
          finished: finished
        })
        
        console.log(`✅ 资产列表加载成功，共 ${newAssetList.length}/${total} 条`)
      } else {
        throw new Error(response?.msg || '获取资产列表失败')
      }
    } catch (error) {
      console.error('❌ 加载资产列表失败:', error)
      Toast.fail('获取资产列表失败')
    } finally {
      this.setData({ 
        loading: false,
        refreshing: false
      })
      
      // 停止下拉刷新
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 搜索输入
   */
  onSearchInput(event) {
    const keyword = event.detail
    this.setData({ searchKeyword: keyword })
    
    // 清除之前的定时器
    if (this.data.searchTimer) {
      clearTimeout(this.data.searchTimer)
    }
    
    // 设置新的定时器，实现防抖
    const timer = setTimeout(() => {
      if (keyword.trim()) {
        this.performSearch()
      } else {
        this.loadAssetList(true)
      }
    }, 500)
    
    this.setData({ searchTimer: timer })
  },

  /**
   * 搜索确认
   */
  onSearch() {
    this.performSearch()
  },

  /**
   * 执行搜索
   */
  performSearch() {
    const keyword = this.data.searchKeyword.trim()
    
    if (keyword) {
      // 保存搜索历史
      AssetAPI.saveSearchHistory(keyword)
    }
    
    // 重新加载数据
    this.loadAssetList(true)
  },

  /**
   * 清除搜索
   */
  onSearchClear() {
    this.setData({ searchKeyword: '' })
    this.loadAssetList(true)
  },

  /**
   * 刷新数据
   */
  onRefresh() {
    this.setData({ refreshing: true })
    this.loadAssetList(true)
  },

  /**
   * 加载更多
   */
  onLoadMore() {
    if (this.data.loading || this.data.finished) {
      return
    }
    
    // 加载下一页数据
    this.setData({
      'pageInfo.current': this.data.pageInfo.current + 1
    })
    
    this.loadAssetList()
  },

  /**
   * 资产项点击
   */
  onAssetItemClick(event) {
    const asset = event.currentTarget.dataset.asset
    console.log('点击资产项:', asset)

    // 跳转到资产详情页面
    wx.navigateTo({
      url: `/pages/asset/detail/detail?assetId=${asset.assetId}`,
      fail: () => {
        Toast.fail('资产详情功能开发中')
      }
    })
  },

  /**
   * 扫码搜索
   */
  onScanCode() {
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res)
        const scanContent = res.result
        
        // 设置搜索关键词并搜索
        this.setData({ searchKeyword: scanContent })
        this.performSearch()
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        Toast.fail('扫码失败')
      }
    })
  },

  // ==================== 筛选相关方法 ====================

  /**
   * 显示筛选弹窗
   */
  showFilterPopup() {
    this.setData({
      showFilter: true,
      tempFilterParams: { ...this.data.filterParams }
    })
  },

  /**
   * 隐藏筛选弹窗
   */
  hideFilterPopup() {
    this.setData({ showFilter: false })
  },

  /**
   * 状态选择变化
   */
  onStatusChange(event) {
    const { value, index } = event.detail
    this.setData({
      'tempFilterParams.status': value.value,
      selectedStatusIndex: index
    })
  },

  /**
   * 类型输入变化
   */
  onTypeChange(event) {
    this.setData({
      'tempFilterParams.assetType': event.detail
    })
  },

  /**
   * 位置输入变化
   */
  onLocationChange(event) {
    this.setData({
      'tempFilterParams.location': event.detail
    })
  },

  /**
   * 应用筛选
   */
  applyFilter() {
    const filterParams = { ...this.data.tempFilterParams }
    
    // 移除空值
    Object.keys(filterParams).forEach(key => {
      if (!filterParams[key]) {
        delete filterParams[key]
      }
    })
    
    const hasActiveFilters = Object.keys(filterParams).length > 0
    
    this.setData({
      filterParams: filterParams,
      hasActiveFilters: hasActiveFilters,
      showFilter: false
    })
    
    // 重新加载数据
    this.loadAssetList(true)
    
    Toast.success('筛选条件已应用')
  },

  /**
   * 重置筛选
   */
  resetFilter() {
    this.setData({
      tempFilterParams: {},
      selectedStatusIndex: 0
    })
  },

  /**
   * 清除所有筛选
   */
  clearAllFilters() {
    this.setData({
      filterParams: {},
      hasActiveFilters: false
    })
    
    this.loadAssetList(true)
    Toast.success('筛选条件已清除')
  },

  /**
   * 清除状态筛选
   */
  clearStatusFilter() {
    const filterParams = { ...this.data.filterParams }
    delete filterParams.status
    
    this.setData({
      filterParams: filterParams,
      hasActiveFilters: Object.keys(filterParams).length > 0
    })
    
    this.loadAssetList(true)
  },

  /**
   * 清除类型筛选
   */
  clearTypeFilter() {
    const filterParams = { ...this.data.filterParams }
    delete filterParams.assetType
    
    this.setData({
      filterParams: filterParams,
      hasActiveFilters: Object.keys(filterParams).length > 0
    })
    
    this.loadAssetList(true)
  },

  /**
   * 清除位置筛选
   */
  clearLocationFilter() {
    const filterParams = { ...this.data.filterParams }
    delete filterParams.location
    
    this.setData({
      filterParams: filterParams,
      hasActiveFilters: Object.keys(filterParams).length > 0
    })
    
    this.loadAssetList(true)
  },

  /**
   * 获取状态名称
   */
  getStatusName(status) {
    return AssetAPI.getAssetStatusName(status)
  }
})
