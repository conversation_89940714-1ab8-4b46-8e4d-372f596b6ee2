/* pages/asset/list/list.wxss */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索区域 */
.search-section {
  background: white;
  padding: 16rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 0 8rpx;
}

/* 筛选标签 */
.filter-tags {
  background: white;
  padding: 16rpx;
  margin-bottom: 16rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  align-items: center;
}

.clear-all-btn {
  margin-left: auto;
}

/* 列表区域 */
.list-section {
  flex: 1;
  height: calc(100vh - 120rpx);
}

.scroll-container {
  height: 100%;
  padding: 0 16rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

/* 资产列表 */
.asset-list {
  padding-bottom: 40rpx;
}

.asset-item {
  margin-bottom: 16rpx !important;
  border-radius: 16rpx !important;
  overflow: hidden !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06) !important;
}

.asset-content {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  gap: 24rpx;
  width: 100%;
}

.asset-image {
  flex-shrink: 0;
}

.asset-info {
  flex: 1;
  min-width: 0;
}

.asset-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.asset-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
  margin-right: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.asset-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.label {
  color: #969799;
  min-width: 80rpx;
  margin-right: 16rpx;
}

.value {
  color: #646566;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.value.price {
  color: #ee0a24;
  font-weight: 600;
}

.asset-arrow {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
}

.load-more-text {
  font-size: 26rpx;
  color: #969799;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #c8c9cc;
}

/* 筛选弹窗 */
.filter-popup {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #ebedf0;
}

.filter-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #323233;
}

.filter-content {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 32rpx;
}

.filter-label {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16rpx;
}

.filter-footer {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  border-top: 1rpx solid #ebedf0;
}

.reset-btn,
.apply-btn {
  flex: 1;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .asset-content {
    padding: 20rpx;
    gap: 20rpx;
  }
  
  .asset-name {
    font-size: 30rpx;
  }
  
  .detail-row {
    font-size: 24rpx;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .search-section,
  .filter-tags,
  .asset-item {
    background-color: #2a2a2a !important;
  }
  
  .asset-name {
    color: #ffffff;
  }
  
  .label {
    color: #999999;
  }
  
  .value {
    color: #cccccc;
  }
}
