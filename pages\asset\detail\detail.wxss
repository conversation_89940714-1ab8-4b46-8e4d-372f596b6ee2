/* pages/asset/detail/detail.wxss */
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

/* 详情内容 */
.detail-content {
  padding-bottom: 40rpx;
}

/* 基本信息区域 */
.basic-info-section {
  background: white;
  margin-bottom: 16rpx;
  padding: 32rpx;
}

/* 资产图片 */
.asset-images {
  margin-bottom: 32rpx;
}

.image-scroll {
  width: 100%;
  white-space: nowrap;
}

.image-list {
  display: inline-flex;
  gap: 16rpx;
}

.image-item {
  flex-shrink: 0;
}

/* 资产标题 */
.asset-header {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.asset-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
}

.asset-name {
  font-size: 40rpx;
  font-weight: 600;
  color: #323233;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.asset-code {
  font-size: 28rpx;
  color: #646566;
}

/* 标签页内容 */
.tab-content {
  padding: 16rpx;
}

/* 维护记录区域 */
.maintenance-section {
  margin-bottom: 32rpx;
}

.maintenance-section:last-child {
  margin-bottom: 0;
}

/* 任务信息 */
.task-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.task-date {
  font-size: 24rpx;
  color: #969799;
}

.completed-date {
  font-size: 22rpx;
  color: #07c160;
  margin-left: 8rpx;
}

/* 图片预览弹窗 */
.image-preview-popup {
  background: transparent !important;
}

.image-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
  padding: 32rpx;
}

.preview-actions {
  display: flex;
  gap: 16rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .basic-info-section {
    padding: 24rpx;
  }
  
  .asset-name {
    font-size: 36rpx;
  }
  
  .asset-code {
    font-size: 26rpx;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .page-container {
    background-color: #1a1a1a;
  }
  
  .basic-info-section {
    background-color: #2a2a2a;
  }
  
  .asset-name {
    color: #ffffff;
  }
  
  .asset-code {
    color: #cccccc;
  }
  
  .task-date {
    color: #999999;
  }
}
