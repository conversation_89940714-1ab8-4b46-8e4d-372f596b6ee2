// utils/asset-api.js
// 资产管理相关API接口封装

import { request } from './request.js'

/**
 * 资产管理API服务
 */
export class AssetAPI {
  
  // ==================== 资产台账相关 ====================

  /**
   * 获取资产列表
   * @param {Object} params 查询参数
   * @param {number} params.pageNum 页码
   * @param {number} params.pageSize 页大小
   * @param {string} params.assetName 资产名称
   * @param {string} params.assetCode 资产编码
   * @param {string} params.assetType 资产类型
   * @param {number} params.status 资产状态
   * @param {string} params.location 存放位置
   * @param {string} params.deptName 所属部门
   * @param {string} params.keyword 关键词搜索
   * @returns {Promise}
   */
  static getAssetList(params = {}) {
    return request({
      url: '/asset/ledger/list',
      method: 'POST',
      data: {
        pageNum: 1,
        pageSize: 20,
        ...params
      }
    })
  }

  /**
   * 获取资产详情
   * @param {string} assetId 资产ID
   * @returns {Promise}
   */
  static getAssetDetail(assetId) {
    return request({
      url: '/asset/ledger/detail',
      method: 'GET',
      data: { assetId }
    })
  }

  /**
   * 获取资产详情（含维护信息）
   * @param {string} assetId 资产ID
   * @returns {Promise}
   */
  static getAssetDetailWithMaintenance(assetId) {
    return request({
      url: '/asset/ledger/detailWithMaintenance',
      method: 'GET',
      data: { assetId }
    })
  }

  /**
   * 获取资产关联备件
   * @param {string} assetId 资产ID
   * @returns {Promise}
   */
  static getAssetParts(assetId) {
    return request({
      url: `/asset/part/relation/parts/${assetId}`,
      method: 'GET'
    })
  }

  /**
   * 根据资产编码搜索资产
   * @param {string} assetCode 资产编码
   * @returns {Promise}
   */
  static searchByCode(assetCode) {
    return this.getAssetList({
      assetCode: assetCode,
      pageSize: 1
    })
  }

  /**
   * 关键词搜索资产
   * @param {string} keyword 搜索关键词
   * @param {Object} options 搜索选项
   * @returns {Promise}
   */
  static searchAssets(keyword, options = {}) {
    return this.getAssetList({
      keyword: keyword,
      pageNum: options.pageNum || 1,
      pageSize: options.pageSize || 20,
      ...options
    })
  }

  // ==================== 资产状态相关 ====================

  /**
   * 获取资产状态选项
   * @returns {Array} 资产状态选项
   */
  static getAssetStatusOptions() {
    return [
      { value: 1, label: '正常', color: '#07c160' },
      { value: 2, label: '维修中', color: '#ff976a' },
      { value: 3, label: '报废', color: '#ee0a24' },
      { value: 4, label: '闲置', color: '#969799' }
    ]
  }

  /**
   * 获取资产状态名称
   * @param {number} status 状态值
   * @returns {string} 状态名称
   */
  static getAssetStatusName(status) {
    const statusMap = {
      1: '正常',
      2: '维修中',
      3: '报废',
      4: '闲置'
    }
    return statusMap[status] || '未知'
  }

  /**
   * 获取资产状态颜色
   * @param {number} status 状态值
   * @returns {string} 状态颜色
   */
  static getAssetStatusColor(status) {
    const colorMap = {
      1: '#07c160',
      2: '#ff976a',
      3: '#ee0a24',
      4: '#969799'
    }
    return colorMap[status] || '#969799'
  }

  /**
   * 获取资产状态类型（用于van-tag）
   * @param {number} status 状态值
   * @returns {string} 状态类型
   */
  static getAssetStatusType(status) {
    const typeMap = {
      1: 'success',
      2: 'warning',
      3: 'danger',
      4: 'default'
    }
    return typeMap[status] || 'default'
  }

  // ==================== 数据格式化相关 ====================

  /**
   * 格式化资产列表数据
   * @param {Array} assetList 原始资产列表
   * @returns {Array} 格式化后的资产列表
   */
  static formatAssetList(assetList) {
    if (!Array.isArray(assetList)) {
      return []
    }

    return assetList.map(asset => ({
      ...asset,
      statusName: this.getAssetStatusName(asset.status),
      statusColor: this.getAssetStatusColor(asset.status),
      statusType: this.getAssetStatusType(asset.status),
      // 格式化价格
      formattedPrice: asset.purchasePrice ? `¥${Number(asset.purchasePrice).toFixed(2)}` : '未设置',
      // 格式化日期
      formattedPurchaseDate: asset.purchaseDate ? this.formatDate(asset.purchaseDate) : '未设置'
    }))
  }

  /**
   * 格式化日期
   * @param {string} dateString 日期字符串
   * @returns {string} 格式化后的日期
   */
  static formatDate(dateString) {
    if (!dateString) return ''
    
    try {
      const date = new Date(dateString)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    } catch (error) {
      console.error('日期格式化失败:', error)
      return dateString
    }
  }

  // ==================== 搜索历史相关 ====================

  /**
   * 保存搜索历史
   * @param {string} keyword 搜索关键词
   */
  static saveSearchHistory(keyword) {
    if (!keyword || !keyword.trim()) return

    try {
      const history = this.getSearchHistory()
      const trimmedKeyword = keyword.trim()
      
      // 移除重复项
      const filteredHistory = history.filter(item => item !== trimmedKeyword)
      
      // 添加到开头
      const newHistory = [trimmedKeyword, ...filteredHistory].slice(0, 10) // 最多保存10条
      
      wx.setStorageSync('asset_search_history', newHistory)
    } catch (error) {
      console.error('保存搜索历史失败:', error)
    }
  }

  /**
   * 获取搜索历史
   * @returns {Array} 搜索历史列表
   */
  static getSearchHistory() {
    try {
      return wx.getStorageSync('asset_search_history') || []
    } catch (error) {
      console.error('获取搜索历史失败:', error)
      return []
    }
  }

  /**
   * 清除搜索历史
   */
  static clearSearchHistory() {
    try {
      wx.removeStorageSync('asset_search_history')
    } catch (error) {
      console.error('清除搜索历史失败:', error)
    }
  }

  /**
   * 删除单条搜索历史
   * @param {string} keyword 要删除的关键词
   */
  static removeSearchHistory(keyword) {
    try {
      const history = this.getSearchHistory()
      const newHistory = history.filter(item => item !== keyword)
      wx.setStorageSync('asset_search_history', newHistory)
    } catch (error) {
      console.error('删除搜索历史失败:', error)
    }
  }
}

// 导出默认实例
export default AssetAPI
